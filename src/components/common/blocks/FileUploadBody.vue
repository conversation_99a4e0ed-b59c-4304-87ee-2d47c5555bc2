<template>
  <div class="q-ml-md">
    <div class="row items-center q-mb-md">
      <div class="col-8">
        <q-item-label class="q-mr-sm">อนุญาตไฟล์บางประเภทเท่านั้น</q-item-label>
      </div>
      <div class="col-4">
        <q-toggle
          v-model="restrictFileTypes"
          color="primary"
          @update:model-value="handleRestrictFileTypesChange"
        />
      </div>
    </div>

    <!-- Checkboxes สำหรับเลือกประเภทไฟล์ (แสดงเมื่อ toggle เปิด) -->
    <div v-if="restrictFileTypes" class="q-ml-md q-mb-md">
      <div class="row">
        <div class="col-3 q-mr-xl">
          <q-checkbox
            v-model="fileTypes.docx"
            label="เอกสาร (.docx)"
            color="primary"
            @update:model-value="handleFileTypeChange"
          />
          <q-checkbox
            v-model="spreadsheetsSelected"
            label="สเปรตชีต (.xlsx, csv)"
            color="primary"
            @update:model-value="handleFileTypeChange"
          />
          <q-checkbox
            v-model="fileTypes.pdf"
            label="PDF"
            color="primary"
            @update:model-value="handleFileTypeChange"
          />
          <q-checkbox
            v-model="fileTypes.mp4"
            label="วีดิโอ"
            color="primary"
            @update:model-value="handleFileTypeChange"
          />
        </div>
        <div class="col-3 q-mr-xl">
          <q-checkbox
            v-model="fileTypes.pptx"
            label="งานนำเสนอ (.pptx)"
            color="primary"
            @update:model-value="handleFileTypeChange"
          />

          <q-checkbox
            v-model="imagesSelected"
            label="รูปภาพ (.png, jpg, jpeg)"
            color="primary"
            @update:model-value="handleFileTypeChange"
          />
          <q-checkbox
            v-model="fileTypes.mp3"
            label="เสียง (.mp3)"
            color="primary"
            @update:model-value="handleFileTypeChange"
          />
        </div>
      </div>
    </div>

    <div class="row items-center q-mb-md">
      <div class="col-8"><q-item-label class="q-mr-sm">จำนวนไฟล์สูงสุด</q-item-label></div>
      <div class="col-4">
        <q-select
          v-model="maxFiles"
          :options="maxFilesOptions"
          dense
          style="width: 100px"
          @update:model-value="handleMaxFilesChange"
        />
      </div>
    </div>

    <div class="row items-center q-mb-md">
      <div class="col-8">
        <q-item-label class="q-mr-sm">ขนาดไฟล์สูงสุด</q-item-label>
      </div>
      <div class="col-4">
        <q-select
          v-model="maxFileSize"
          :options="maxFileSizeOptions"
          dense
          style="width: 100px"
          @update:model-value="handleMaxFileSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import type { ItemBlock } from 'src/types/models';
import { useGlobalStore } from 'src/stores/global';

// Props
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
}>();

// Note: We implement our own auto-save logic instead of using injected functions

// Global store for save operations
const globalStore = useGlobalStore();

// Reactive state
const restrictFileTypes = ref<boolean>(false);
const fileTypes = ref({
  docx: false,
  xlsx: false,
  csv: false,
  pdf: false,
  pptx: false,
  png: false,
  jpg: false,
  jpeg: false,
  mp3: false,
  mp4: false,
});

// MIME type mappings for file types
const fileTypeToMimeType: Record<string, string> = {
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  csv: 'text/csv',
  pdf: 'application/pdf',
  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  png: 'image/png',
  jpg: 'image/jpeg',
  jpeg: 'image/jpeg',
  mp3: 'audio/mpeg',
  mp4: 'video/mp4',
};

// Reverse mapping from MIME type to file type key
const mimeTypeToFileType: Record<string, string> = {
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'text/csv': 'csv',
  'application/pdf': 'pdf',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
  'image/png': 'png',
  'image/jpeg': 'jpeg', // Note: both jpg and jpeg map to image/jpeg, but we'll use jpeg as primary
  'audio/mpeg': 'mp3',
  'video/mp4': 'mp4',
};

const spreadsheetsSelected = computed({
  get: () => fileTypes.value.xlsx && fileTypes.value.csv,
  set: (value: boolean) => {
    fileTypes.value.xlsx = value;
    fileTypes.value.csv = value;
  },
});

const imagesSelected = computed({
  get: () => fileTypes.value.png && fileTypes.value.jpg && fileTypes.value.jpeg,
  set: (value: boolean) => {
    fileTypes.value.png = value;
    fileTypes.value.jpg = value;
    fileTypes.value.jpeg = value;
  },
});

const maxFiles = ref<number>(1);
const maxFilesOptions = [1, 5, 10];

const maxFileSize = ref<string>('1 MB');
const maxFileSizeOptions = ['1 MB', '10 MB', '100 MB', '1 GB', '10 GB'];

// Debounce timer for auto-save
let saveTimeout: NodeJS.Timeout | null = null;

// Get the first question from the itemBlock (upload questions are stored in questions array)
const uploadQuestion = computed(() => {
  return props.itemBlock.questions?.[0];
});

// Utility functions for data conversion
function parseAcceptFileString(acceptFile: string | undefined): void {
  if (!acceptFile) {
    restrictFileTypes.value = false;
    return;
  }

  restrictFileTypes.value = true;
  const mimeTypes = acceptFile.split(',').map((type) => type.trim());

  // Reset all file types
  Object.keys(fileTypes.value).forEach((key) => {
    fileTypes.value[key as keyof typeof fileTypes.value] = false;
  });

  // Set file types based on MIME types
  mimeTypes.forEach((mimeType) => {
    const fileTypeKey = mimeTypeToFileType[mimeType];
    if (fileTypeKey && fileTypeKey in fileTypes.value) {
      fileTypes.value[fileTypeKey as keyof typeof fileTypes.value] = true;
      // Also set jpg when jpeg is found (for UI consistency)
      if (fileTypeKey === 'jpeg') {
        fileTypes.value.jpg = true;
      }
    }
  });
}

function generateAcceptFileString(): string {
  if (!restrictFileTypes.value) return '';

  const selectedMimeTypes: string[] = [];

  Object.entries(fileTypes.value).forEach(([key, value]) => {
    if (value) {
      const mimeType = fileTypeToMimeType[key];
      if (mimeType && !selectedMimeTypes.includes(mimeType)) {
        selectedMimeTypes.push(mimeType);
      }
    }
  });

  return selectedMimeTypes.join(',');
}

function convertSizeToBytes(sizeString: string): number {
  const [value, unit] = sizeString.split(' ');
  const numValue = parseFloat(value || '1');
  const unitStr = unit || 'MB';

  switch (unitStr.toUpperCase()) {
    case 'MB':
      return numValue * 1024 * 1024;
    case 'GB':
      return numValue * 1024 * 1024 * 1024;
    default:
      return numValue * 1024 * 1024; // Default to MB
  }
}

function convertBytesToSize(bytes: number | undefined): string {
  if (!bytes) return '1 MB';

  if (bytes >= 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(0)} GB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(0)} MB`;
  }
}

// Auto-save function with debouncing
async function performUploadSettingsSave() {
  if (!uploadQuestion.value?.id) return;

  try {
    globalStore.startSaveOperation('Saving...');

    // Import AssessmentService for question updates
    const { AssessmentService } = await import('src/services/asm/assessmentService');
    const assessmentService = new AssessmentService(props.type || 'evaluate');

    // Prepare the update payload
    const updatePayload = {
      acceptFile: generateAcceptFileString(),
      uploadLimit: maxFiles.value,
      sizeLimit: convertSizeToBytes(maxFileSize.value),
      itemBlockId: props.itemBlock.id,
    };

    console.log('🔄 Saving upload settings:', {
      questionId: uploadQuestion.value.id,
      payload: updatePayload,
    });

    // Call the updateQuestion API
    await assessmentService.updateQuestion(uploadQuestion.value.id, updatePayload);

    globalStore.completeSaveOperation(true, 'Saved successfully');
  } catch (error) {
    console.error('❌ Upload settings save failed:', error);
    globalStore.completeSaveOperation(false, 'Save failed');
  }
}

// Debounced save function
function debouncedSave() {
  if (saveTimeout) {
    clearTimeout(saveTimeout);
  }
  saveTimeout = setTimeout(() => {
    performUploadSettingsSave().catch(console.error);
  }, 800);
}

// Event handlers
function handleRestrictFileTypesChange() {
  debouncedSave();
}

function handleFileTypeChange() {
  debouncedSave();
}

function handleMaxFilesChange() {
  debouncedSave();
}

function handleMaxFileSizeChange() {
  debouncedSave();
}

// Initialize component with existing data
function initializeFromQuestion() {
  if (!uploadQuestion.value) return;

  // Initialize file type restrictions
  parseAcceptFileString(uploadQuestion.value.acceptFile);

  // Initialize max files
  if (uploadQuestion.value.uploadLimit) {
    maxFiles.value = uploadQuestion.value.uploadLimit;
  }

  // Initialize max file size
  if (uploadQuestion.value.sizeLimit) {
    maxFileSize.value = convertBytesToSize(uploadQuestion.value.sizeLimit);
  }
}

// Watch for changes in the upload question to reinitialize
watch(
  () => uploadQuestion.value,
  (newQuestion) => {
    if (newQuestion) {
      initializeFromQuestion();
    }
  },
  { immediate: true },
);

// Initialize on mount
onMounted(() => {
  initializeFromQuestion();
});
</script>

<style scoped>
.q-toggle {
  margin-left: 10px;
}

.q-checkbox {
  min-width: 200px;
}

.q-select {
  min-width: 10px;
}
</style>
